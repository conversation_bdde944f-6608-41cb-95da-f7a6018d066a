use crate::ServiceParams;

#[cfg(target_os = "windows")]
pub async fn install_service(params: ServiceParams) -> anyhow::Result<()> {
    // Install the service first
    service::install(params)?;

    Ok(())
}

#[cfg(windows)]
mod service {
    use std::path::Path;

    use crate::ServiceParams;
    use std::{io::Write as _, process::Command};

    use anyhow::Context;

    const SERVICE_NAME: &str = "iroh-ssh";
    const PROFILE_SERVICE_DIR: &str = "C:\\Windows\\ServiceProfiles\\iroh-ssh";
    const BINARY_DIR: &str = "C:\\ProgramData\\iroh-ssh";

    const NSSM_BYTES: &[u8] = include_bytes!("../../win/nssm.exe");

    fn init_nssm() -> anyhow::Result<Command> {
        let mut temp_exe = tempfile::Builder::new()
            .prefix("nssm-")
            .suffix(".exe")
            .tempfile()?;
        temp_exe.write_all(NSSM_BYTES)?;
        temp_exe.flush()?;
        let path = temp_exe.path().to_path_buf();
        temp_exe.keep()?;

        
        Ok(Command::new(path))
    }

    pub fn install(service_params: ServiceParams) -> anyhow::Result<()> {
        let mut nssm = init_nssm()?;

        let profile_ssh_dir = std::path::Path::new(PROFILE_SERVICE_DIR)
            .join(".ssh");
        let service_binary_dir = std::path::Path::new(BINARY_DIR);


        std::fs::create_dir_all(&profile_ssh_dir)
            .with_context(|| format!("Failed to create service directory at {:?}", &profile_ssh_dir))?;
        std::fs::create_dir_all(&service_binary_dir)
            .with_context(|| format!("Failed to create binary directory {:?}", &service_binary_dir))?;

        std::fs::copy(std::env::current_exe()?, service_binary_dir.join(format!("{}.exe", SERVICE_NAME)))?;

        /*
        # nssm making a decent Windows service
        nssm.exe install iroh-ssh C:\ProgramData\iroh-ssh\iroh-ssh.exe
        nssm.exe set iroh-ssh AppParameters server
        nssm.exe set iroh-ssh AppDirectory C:\ProgramData\iroh-ssh
        nssm.exe set iroh-ssh AppExit Default Restart
        nssm.exe set iroh-ssh AppPriority HIGH_PRIORITY_CLASS
        nssm.exe set iroh-ssh AppStdout C:\ProgramData\iroh-ssh\iroh-ssh.log
        nssm.exe set iroh-ssh AppStderr C:\ProgramData\iroh-ssh\iroh-ssh.error.log
        nssm.exe set iroh-ssh AppTimestampLog 1
        nssm.exe set iroh-ssh DependOnService :sshd
        nssm.exe set iroh-ssh Description "SSHD over Iroh"
        nssm.exe set iroh-ssh DisplayName iroh-ssh
        
        nssm.exe set iroh-ssh ObjectName "NT Service\iroh-ssh"
        nssm.exe set iroh-ssh Start SERVICE_AUTO_START
        nssm.exe set iroh-ssh Type SERVICE_WIN32_OWN_PROCESS
         */
        println!("1: {}", nssm.get_program().display());
        println!("{:#?}", nssm.args(["install", SERVICE_NAME, &format!("{BINARY_DIR}\\{SERVICE_NAME}.exe")]).output());
        println!("2");
        nssm.args(["set", SERVICE_NAME, "AppParameters",  &format!("server --persist --port {}", &service_params.ssh_port)]).output()?;
        println!("3");
        nssm.args(["set", SERVICE_NAME, "AppDirectory",  BINARY_DIR]).output()?;
        nssm.args(["set", SERVICE_NAME, "AppExit", "Default","Restart"]).output()?;
        nssm.args(["set", SERVICE_NAME, "AppPriority", "HIGH_PRIORITY_CLASS"]).output()?;
        nssm.args(["set", SERVICE_NAME, "AppStdout", &format!("{BINARY_DIR}\\{SERVICE_NAME}.log")]).output()?;
        nssm.args(["set", SERVICE_NAME, "AppStderr", &format!("{BINARY_DIR}\\{SERVICE_NAME}.error.log")]).output()?;
        nssm.args(["set", SERVICE_NAME, "AppTimestampLog", "1"]).output()?;
        nssm.args(["set", SERVICE_NAME, "DependOnService", ":sshd"]).output()?;
        nssm.args(["set", SERVICE_NAME, "Description", "ssh without ip"]).output()?;
        nssm.args(["set", SERVICE_NAME, "DisplayName", SERVICE_NAME]).output()?;
        nssm.args(["set", SERVICE_NAME, "ObjectName", &format!("NT Service\\{SERVICE_NAME}")]).output()?;
        nssm.args(["set", SERVICE_NAME, "Start", "SERVICE_AUTO_START"]).output()?;
        nssm.args(["set", SERVICE_NAME, "Type", "SERVICE_WIN32_OWN_PROCESS"]).output()?;

        println!("Starting service...");
        println!("{:#?}", std::process::Command::new("start-service").output()?);

        println!("Service installed and started");
    
        Ok(())
    }
}


#[cfg(target_os = "windows")]
mod Nssm {
    
}